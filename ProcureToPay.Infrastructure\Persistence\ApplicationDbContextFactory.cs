using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System.IO;
using System.Reflection;
using ProcureToPay.Infrastructure.Persistence;
using ProcureToPay.Infrastructure.Identity;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using System;
using System.Linq; // Required for LINQ methods like Any() / Length
using ProcureToPay.Domain.Interfaces; // Required for ITenantProvider

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// Factory for creating ApplicationDbContext instances at design time.
    /// Used by EF Core tools when creating migrations and applying them from the command line.
    /// </summary>
    public class ApplicationDbContextFactory : IDesignTimeDbContextFactory<ApplicationDbContext>
    {
        public ApplicationDbContext CreateDbContext(string[] args)
        {
            // Check if connection string is provided in args (from command line)
            string? explicitConnectionString = null;
            if (args != null && args.Length > 0)
            {
                // Look for --connection or -c parameter
                for (int i = 0; i < args.Length - 1; i++)
                {
                    if ((args[i] == "--connection" || args[i] == "-c") && i + 1 < args.Length)
                    {
                        explicitConnectionString = args[i + 1];
                        Console.WriteLine($"---> ApplicationDbContextFactory: Using explicit connection string from command line args.");
                        break;
                    }
                }
            }

            // If explicit connection string is provided, use it directly
            if (!string.IsNullOrEmpty(explicitConnectionString))
            {
                return CreateDbContextWithConnectionString(explicitConnectionString);
            }

            // Otherwise, try to find connection string from configuration
            // --- Start: Multi-level Configuration Search ---
            var configBuilder = new ConfigurationBuilder();
            var searchPaths = new List<string>();

            // 1. Try current directory first
            searchPaths.Add(Directory.GetCurrentDirectory());

            // 2. Try solution directory if we can find it
            string? solutionDirectory = FindSolutionDirectory(AppContext.BaseDirectory);
            if (solutionDirectory != null)
            {
                searchPaths.Add(solutionDirectory);

                // 3. Try Infrastructure project directory
                string infrastructureDir = Path.Combine(solutionDirectory, "ProcureToPay.Infrastructure");
                if (Directory.Exists(infrastructureDir))
                {
                    searchPaths.Add(infrastructureDir);
                }

                // 4. Try WebApp project directory
                string webAppDir = Path.Combine(solutionDirectory, "ProcureToPay.WebApp", "ProcureToPay.WebApp");
                if (Directory.Exists(webAppDir))
                {
                    searchPaths.Add(webAppDir);
                }
            }

            // 5. Try AppContext.BaseDirectory
            searchPaths.Add(AppContext.BaseDirectory);

            // Log search paths
            Console.WriteLine($"---> ApplicationDbContextFactory: Searching for configuration in the following paths:");
            foreach (var path in searchPaths)
            {
                Console.WriteLine($"---> - {path}");
            }

            // Add all potential config files from all search paths
            foreach (var basePath in searchPaths)
            {
                configBuilder
                    .SetBasePath(basePath)
                    .AddJsonFile("appsettings.json", optional: true)
                    .AddJsonFile($"appsettings.Development.json", optional: true)
                    .AddJsonFile($"appsettings.Production.json", optional: true);
            }

            // Add environment variables and user secrets
            configBuilder
                .AddEnvironmentVariables()
                .AddUserSecrets(Assembly.GetExecutingAssembly(), optional: true);

            // Build configuration
            IConfigurationRoot configuration;
            try
            {
                configuration = configBuilder.Build();
                Console.WriteLine($"---> ApplicationDbContextFactory: Successfully built configuration.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"---> ApplicationDbContextFactory: ERROR building configuration: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"---> Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }

            // Get connection string
            string connectionStringName = "postgresdb";
            var connectionString = configuration.GetConnectionString(connectionStringName);
            Console.WriteLine($"---> ApplicationDbContextFactory: Connection string '{connectionStringName}' from config: {(string.IsNullOrEmpty(connectionString) ? "NOT FOUND" : "Found (value hidden)")}");

            if (string.IsNullOrEmpty(connectionString))
            {
                // If connection string is not found, throw a detailed exception
                var searchedPaths = string.Join(", ", searchPaths.Select(p => $"'{p}'"));
                throw new InvalidOperationException(
                    $"Connection string '{connectionStringName}' not found in any configuration file. " +
                    $"Searched in the following paths: {searchedPaths}. " +
                    "Please ensure a valid connection string is configured in appsettings.json, " +
                    "or provide it explicitly using the --connection parameter.");
            }

            return CreateDbContextWithConnectionString(connectionString);
        }

        /// <summary>
        /// Creates a DbContext with the specified connection string.
        /// </summary>
        private ApplicationDbContext CreateDbContextWithConnectionString(string connectionString)
        {
            Console.WriteLine($"---> ApplicationDbContextFactory: Configuring DbContextOptions with Npgsql.");
            var optionsBuilder = new DbContextOptionsBuilder<ApplicationDbContext>();

            try
            {
                optionsBuilder.UseNpgsql(connectionString, sqlOptions =>
                {
                    sqlOptions.MigrationsAssembly(typeof(ApplicationDbContextFactory).Assembly.FullName);
                    // Add retry on failure for design-time operations
                    sqlOptions.EnableRetryOnFailure(
                        maxRetryCount: 5,
                        maxRetryDelay: TimeSpan.FromSeconds(30),
                        errorCodesToAdd: null);
                });

                // Note: Snake case naming convention is now handled in ApplicationDbContext.OnModelCreating()
                // to avoid compatibility issues with EFCore.NamingConventions library in .NET 9 preview
            }
            catch (Exception ex)
            {
                Console.WriteLine($"---> ApplicationDbContextFactory: ERROR configuring Npgsql: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"---> Inner exception: {ex.InnerException.Message}");
                }
                throw;
            }

            Console.WriteLine($"---> ApplicationDbContextFactory: Creating ApplicationDbContext instance with DesignTimeTenantProvider.");
            // Use DesignTimeTenantProvider which returns null for tenant ID to disable query filters
            ITenantProvider designTimeTenantProvider = new DesignTimeTenantProvider();
            return new ApplicationDbContext(optionsBuilder.Options, designTimeTenantProvider);
        }

        // Helper function to find the solution directory (.sln file) by searching upwards
        private static string? FindSolutionDirectory(string? startPath)
        {
            var directory = string.IsNullOrEmpty(startPath) ? null : new DirectoryInfo(startPath);
            while (directory != null && directory.GetFiles("*.sln").Length == 0)
            {
                directory = directory.Parent;
            }
            return directory?.FullName;
        }
    }
}
