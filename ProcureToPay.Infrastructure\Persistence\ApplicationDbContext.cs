﻿using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking; // Required for ChangeTracker
using Microsoft.Extensions.DependencyInjection; // Required for IServiceProvider
using ProcureToPay.Application.Interfaces;
using ProcureToPay.Domain.Entities; // Import your domain entities namespace
using ProcureToPay.Domain.Interfaces; // Assuming ITenantEntity and ITenantProvider interfaces are here
using ProcureToPay.Infrastructure.Identity; // Import your ApplicationUser if it's custom
using System.Linq.Expressions; // Required for LambdaExpression
using System.Reflection; // Required for ApplyConfigurationsFromAssembly
using System.Text; // Required for StringBuilder

namespace ProcureToPay.Infrastructure.Persistence
{
    /// <summary>
    /// The main database context for the application, integrating Identity and Domain entities.
    /// Implements multi-tenancy using a shared database, row-level security approach.
    /// </summary>
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>, IApplicationDbContext // Use your specific ApplicationUser class
    {
        private readonly ITenantProvider _tenantProvider;
        private readonly Guid? _currentTenantId; // Store tenant ID resolved during construction for filtering

        // --- Domain Entity DbSets ---
        // (Keep all existing DbSet properties as they were)
        public DbSet<Budget> Budgets { get; set; } = null!;
        public DbSet<BudgetAllocation> BudgetAllocations { get; set; } = null!;
        public DbSet<Category> Categories { get; set; } = null!;
        public DbSet<Contract> Contracts { get; set; } = null!;
        public DbSet<Customer> Customers { get; set; } = null!;
        public DbSet<DeliveryNote> DeliveryNotes { get; set; } = null!;
        public DbSet<DeliveryNoteLine> DeliveryNoteLines { get; set; } = null!;
        public DbSet<Department> Departments { get; set; } = null!;
        public DbSet<GoodsReceiptNote> GoodsReceiptNotes { get; set; } = null!;
        public DbSet<GoodsReceiptNoteLine> GoodsReceiptNoteLines { get; set; } = null!;
        public DbSet<Invoice> Invoices { get; set; } = null!;
        public DbSet<InvoiceLine> InvoiceLines { get; set; } = null!;
        public DbSet<PaymentTransaction> PaymentTransactions { get; set; } = null!;
        public DbSet<ProcurementWorkflow> ProcurementWorkflows { get; set; } = null!;
        public DbSet<ProcurementWorkflowStep> ProcurementWorkflowSteps { get; set; } = null!;
        public DbSet<ProductDefinition> ProductDefinitions { get; set; } = null!;
        public DbSet<Product> Products { get; set; } = null!;
        public DbSet<Project> Projects { get; set; } = null!;
        public DbSet<PurchaseOrder> PurchaseOrders { get; set; } = null!;
        public DbSet<PurchaseOrderLine> PurchaseOrderLines { get; set; } = null!;
        public DbSet<PurchaseRequisition> PurchaseRequisitions { get; set; } = null!;
        public DbSet<PurchaseRequisitionLine> PurchaseRequisitionLines { get; set; } = null!;
        public DbSet<RequestForInformation> RequestsForInformation { get; set; } = null!;
        public DbSet<RequestForProposal> RequestsForProposal { get; set; } = null!;
        public DbSet<RequestForQuote> RequestsForQuote { get; set; } = null!;
        public DbSet<RequestForQuoteLine> RequestForQuoteLines { get; set; } = null!;
        public DbSet<ReturnAuthorization> ReturnAuthorizations { get; set; } = null!;
        public DbSet<ReturnAuthorizationLine> ReturnAuthorizationLines { get; set; } = null!;
        public DbSet<SalesOrder> SalesOrders { get; set; } = null!;
        public DbSet<SalesOrderLine> SalesOrderLines { get; set; } = null!;
        public DbSet<SalesTerritory> SalesTerritories { get; set; } = null!;
        public DbSet<SubmittalReview> SubmittalReviews { get; set; } = null!;
        public DbSet<Supplier> Suppliers { get; set; } = null!;
        public DbSet<TechnicalSubmittal> TechnicalSubmittals { get; set; } = null!;
        public DbSet<Tenant> Tenants { get; set; } = null!; // Tenant entity itself might not implement ITenantEntity
        public DbSet<TenantProduct> TenantProducts { get; set; } = null!;
        public DbSet<Vendor> Vendors { get; set; } = null!;
        public DbSet<VendorProduct> VendorProducts { get; set; } = null!;
        public DbSet<VendorProposal> VendorProposals { get; set; } = null!;
        public DbSet<TestEntity> TestEntities { get; set; } = null!;
        // Add other DbSets as needed...


        /// <summary>
        /// Initializes a new instance of the <see cref="ApplicationDbContext"/> class.
        /// </summary>
        /// <param name="options">The options to be used by a <see cref="DbContext"/>.</param>
        /// <param name="tenantProvider">Service to resolve the current tenant ID.</param>
        public ApplicationDbContext(
            DbContextOptions<ApplicationDbContext> options,
            ITenantProvider tenantProvider) // Inject the tenant provider
            : base(options)
        {
            _tenantProvider = tenantProvider ?? throw new ArgumentNullException(nameof(tenantProvider));
            _currentTenantId = _tenantProvider.GetCurrentTenantId(); // Resolve tenant ID once per context instance
        }

        /// <summary>
        /// Configures the schema needed for the identity framework, applies entity configurations,
        /// and sets up global query filters for multi-tenancy.
        /// </summary>
        /// <param name="modelBuilder">The builder being used to construct the model for this context.</param>
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            // --- Apply Identity Configurations FIRST ---
            base.OnModelCreating(modelBuilder);

            // --- Apply Domain Entity Configurations from Infrastructure Assembly ---
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            // --- Apply Snake Case Naming Convention ---
            ApplySnakeCaseNamingConvention(modelBuilder);

            // --- Global Query Filter for Tenant Isolation ---
            ConfigureTenantQueryFilters(modelBuilder);

            // --- Default Schema (Optional) ---
            // modelBuilder.HasDefaultSchema("p2p");

            // --- PostgreSQL Extensions (Optional) ---
            // modelBuilder.HasPostgresExtension("uuid-ossp");
        }

        /// <summary>
        /// Configures global query filters for entities implementing ITenantEntity.
        /// </summary>
        /// <param name="modelBuilder">The model builder.</param>
        private void ConfigureTenantQueryFilters(ModelBuilder modelBuilder)
        {
            // Iterate through all entity types in the model
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                // Check if the entity implements the ITenantEntity interface
                if (typeof(ITenantEntity).IsAssignableFrom(entityType.ClrType))
                {
                    // Build the lambda expression for the query filter dynamically
                    var parameter = Expression.Parameter(entityType.ClrType, "e");

                    // IMPORTANT: For migrations and design-time operations, we need to allow all entities
                    // to be visible regardless of tenant. This is critical for EF Core to properly
                    // generate and apply migrations.

                    // Check if we're in a migration or design-time context
                    bool isMigrationOrDesignTime = _currentTenantId == null;

                    if (isMigrationOrDesignTime)
                    {
                        // During migrations or design-time operations, don't apply tenant filtering
                        // This allows EF Core to see all entities regardless of tenant
                        var trueExpression = Expression.Constant(true);
                        var lambda = Expression.Lambda(trueExpression, parameter);
                        modelBuilder.Entity(entityType.ClrType).HasQueryFilter(lambda);
                    }
                    else
                    {
                        // In normal operation with a valid tenant, apply tenant filtering
                        var property = Expression.Property(parameter, nameof(ITenantEntity.TenantId));
                        var tenantIdValue = Expression.Constant(_currentTenantId);
                        var equal = Expression.Equal(property, tenantIdValue);
                        var lambda = Expression.Lambda(equal, parameter);
                        modelBuilder.Entity(entityType.ClrType).HasQueryFilter(lambda);
                    }
                }
            }
        }

        /// <summary>
        /// Applies snake_case naming convention to all entity properties that don't already have explicit column names.
        /// This replaces the EFCore.NamingConventions library to avoid compatibility issues with .NET 9 preview.
        /// </summary>
        /// <param name="modelBuilder">The model builder.</param>
        private void ApplySnakeCaseNamingConvention(ModelBuilder modelBuilder)
        {
            foreach (var entityType in modelBuilder.Model.GetEntityTypes())
            {
                // Skip owned entities to avoid conflicts with their owner's configuration
                if (entityType.IsOwned())
                    continue;

                // Apply snake_case to table names if not explicitly set and not already snake_case
                var tableName = entityType.GetTableName();
                if (tableName != null && !tableName.Contains('_') && tableName != ConvertToSnakeCase(tableName))
                {
                    entityType.SetTableName(ConvertToSnakeCase(tableName));
                }

                // Apply snake_case to column names for properties that don't have explicit column names
                foreach (var property in entityType.GetProperties())
                {
                    var columnName = property.GetColumnName();
                    var propertyName = property.Name;

                    // Only apply snake_case if:
                    // 1. Column name equals property name (not explicitly set)
                    // 2. Property name is not already snake_case
                    // 3. Property is not a shadow property (like foreign keys)
                    if (columnName == propertyName &&
                        !propertyName.Contains('_') &&
                        !property.IsShadowProperty())
                    {
                        property.SetColumnName(ConvertToSnakeCase(propertyName));
                    }
                }

                // Apply snake_case to foreign key constraint names (be more conservative)
                foreach (var key in entityType.GetKeys())
                {
                    var keyName = key.GetName();
                    if (keyName != null && !keyName.Contains('_') && !keyName.StartsWith("PK_"))
                    {
                        key.SetName(ConvertToSnakeCase(keyName));
                    }
                }

                // Apply snake_case to index names (be more conservative)
                foreach (var index in entityType.GetIndexes())
                {
                    var indexName = index.GetDatabaseName();
                    if (indexName != null && !indexName.Contains('_') && !indexName.StartsWith("IX_"))
                    {
                        index.SetDatabaseName(ConvertToSnakeCase(indexName));
                    }
                }
            }
        }

        /// <summary>
        /// Converts a PascalCase or camelCase string to snake_case.
        /// </summary>
        /// <param name="input">The input string to convert.</param>
        /// <returns>The snake_case version of the input string.</returns>
        private static string ConvertToSnakeCase(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            var result = new StringBuilder();
            for (int i = 0; i < input.Length; i++)
            {
                char currentChar = input[i];

                if (char.IsUpper(currentChar))
                {
                    // Add underscore before uppercase letters (except for the first character)
                    if (i > 0 && input[i - 1] != '_')
                    {
                        result.Append('_');
                    }
                    result.Append(char.ToLowerInvariant(currentChar));
                }
                else
                {
                    result.Append(currentChar);
                }
            }

            return result.ToString();
        }


        /// <summary>
        /// Overrides SaveChanges to automatically set the TenantId for added/modified tenant entities.
        /// </summary>
        /// <returns>The number of state entries written to the database.</returns>
        public override int SaveChanges()
        {
            SetTenantIdBeforeSaving();
            return base.SaveChanges();
        }

        /// <summary>
        /// Overrides SaveChangesAsync to automatically set the TenantId for added/modified tenant entities.
        /// </summary>
        /// <param name="cancellationToken">A <see cref="CancellationToken"/> to observe while waiting for the task to complete.</param>
        /// <returns>A task that represents the asynchronous save operation. The task result contains the number of state entries written to the database.</returns>
        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            SetTenantIdBeforeSaving();
            return await base.SaveChangesAsync(cancellationToken);
        }

        /// <summary>
        /// Sets the TenantId property for entities implementing ITenantEntity before saving changes.
        /// </summary>
        /// <exception cref="InvalidOperationException">Thrown if TenantId is required but not available from the provider.</exception>
        private void SetTenantIdBeforeSaving()
        {
            // Get all Added or Modified entries that implement ITenantEntity
            var entries = ChangeTracker.Entries<ITenantEntity>()
                .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

            // If there are no tenant entities being modified, we don't need to do anything
            if (!entries.Any())
            {
                return;
            }

            // Check if we're in a migration or design-time context
            bool isMigrationOrDesignTime = _currentTenantId == null;

            if (isMigrationOrDesignTime)
            {
                // During migrations or design-time operations, use a default tenant ID
                // This is necessary for creating the initial schema and applying migrations
                Guid defaultTenantId = Guid.Parse("11111111-1111-1111-1111-111111111111");

                foreach (var entry in entries)
                {
                    // Only set the TenantId if it's not already set
                    if (entry.Entity.TenantId == Guid.Empty)
                    {
                        entry.Entity.TenantId = defaultTenantId;
                    }
                }
            }
            else
            {
                // In normal operation with a valid tenant, set the tenant ID
                // Make sure _currentTenantId is not null before accessing .Value
                if (_currentTenantId.HasValue)
                {
                    foreach (var entry in entries)
                    {
                        entry.Entity.TenantId = _currentTenantId.Value;
                    }
                }
                else
                {
                    // This should not happen in normal operation, but handle it gracefully
                    throw new InvalidOperationException("No tenant ID is available, but tenant entities are being saved. This indicates a configuration issue with the tenant provider.");
                }
            }
        }

        // --- Optional: Override SaveChangesAsync for Auditing ---
        // public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        // {
        //     SetTenantIdBeforeSaving(); // Ensure TenantId is set first
        //     UpdateAuditProperties(); // Then update audit properties
        //     return await base.SaveChangesAsync(cancellationToken);
        // }
        // private void UpdateAuditProperties() { /* ... logic ... */ }
    }
}